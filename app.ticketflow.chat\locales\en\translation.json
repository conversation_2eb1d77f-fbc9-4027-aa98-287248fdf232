{"search.restaurants.products": "Search restaurants and products", "search.products.in": "Search products in {{shop}}", "delivery": "Delivery", "delivery.address": "Delivery address", "delivery.range": "{{times}} min", "delivery.price": "Delivery price", "delivery.time": "Delivery time", "sorted.by": "Sorted by", "filter": "Filter", "recommended": "Recommended", "news.week": "News of the week", "all.restaurants": "All restaurants", "number.of.foods": "{{count}} foods", "popular": "Popular", "foods": "Foods", "orders": "Orders", "liked": "Liked", "order": "Order", "your.orders": "Your orders", "total": "Total", "cart.empty": "Cart is empty", "pickup": "Pickup", "type.here": "Type here", "payment": "Payment", "payment.method": "Payment method", "payment.status": "Payment status", "promo.code": "Promo code", "add": "Add", "enter": "Enter", "subtotal": "Subtotal", "service.fee": "Service fee", "continue.payment": "Continue payment", "more": "More", "working.time": "Working time", "start.group.order": "Start group order", "clear.bag": "Clear bag", "save": "Save", "add.promocode": "Add promo code", "clear": "Clear", "sign.up": "Sign up", "login": "<PERSON><PERSON>", "app.text": "There's more to love in the app.", "dont.have.account": "Don't have an account?", "keep.logged": "Keep me logged in", "forgot.password": "Forgot password", "access.quickly": "or access quickly", "have.account": "Already have an account?", "reset.password": "Reset password", "reset.password.text": "Please provide email address and we'll send you code which you can change your password.", "send": "Send", "enter.otp.code": "Enter OTP code", "enter.code.text": "We are send OTP code to {{phone}}", "send.new": "Send new", "confirm": "Confirm", "restaurant": "Restaurant", "found.number.results": "Found {{count}} results", "enter.delivery.address": "Enter delivery address", "search": "Search", "submit": "Submit", "view.profile": "View profile", "settings": "Settings", "help": "Help", "log.out": "Log out", "profile": "Profile", "date.of.birth": "Date of birth", "update.password": "Update password", "old.password": "Old password", "password.confirmation": "Password confirmation", "cancel": "Cancel", "gender": "Gender", "choose.here": "Choose here", "male": "Male", "female": "Female", "notification": "Notification", "push.notifications": "Push notifications", "on": "On", "off": "Off", "send.news.email": "Send news email", "discount.notifications": "Discount notifications", "order.verify": "Order verify", "back": "Back", "active.orders": "Active orders", "order.history": "Order history", "new": "New", "accepted": "Accepted", "ready": "Ready", "on_a_way": "On a way", "delivered": "Delivered", "cancelled": "Cancelled", "driver": "Driver", "support": "Support", "repeat.order": "Repeat order", "liked.restaurants": "Liked restaurants", "have.questions": "Still have questions?", "questions.text": "Can’t find the answer you’re looking or? Please chat to our friendly team.", "call.support": "Call to support", "group.order.text": "You fully manage the order and confirm the address. Team members can add a product from a location of your choice.", "start": "Start", "copied": "Copied to clipboard!", "group.members": "Group members", "choosing": "Choosing", "clear.cart": "Are you sure to clear the cart?", "rating": "Rating", "special.offers": "Special offers", "free.delivery": "Free delivery", "show": "Show", "all": "All", "languages": "Languages", "currency": "<PERSON><PERSON><PERSON><PERSON>", "no": "No", "yes": "Yes", "order.for.address": "Order for this address?", "replace.cart.prompt": "You can only add items from one restaurant to your shopping cart.", "saved": "Saved", "required": "Required", "passwords.dont.match": "Passwords don't match", "password.should.contain": "Password should contain at least 6 characters", "shop.tax": "Shop tax", "order.tax": "Order tax", "vat.tax": "VAT tax", "today": "Today", "tomorrow": "Tomorrow", "min": "min", "edit": "Edit", "order.details": "Order details", "cancel.order": "Cancel order", "under": "Under", "bonus": "Bonus", "are.you.sure.cancel.order": "Are you sure to cancel this order?", "order.cancelled": "Order cancelled", "wallet": "Wallet", "choose.payment.method": "Please, choose payment method", "refund": "Refund", "leave.feedback": "Leave feedback", "thanks.for.feedback": "Thank you for your feedback!", "order.refund": "Order refund", "why.refund": "Why do you want to refund?", "request.sent": "Request sent successfully!", "request.not.sent": "You request didn't send!", "pending": "Pending", "approved": "Approved", "rejected": "Rejected", "refunds": "Refunds", "products": "Products", "your.comment": "Your comment", "answer": "Answer", "order.id": "Order ID", "go.to.order": "Go to order", "price": "Price", "closed": "Closed", "done": "Done", "manage.group.order": "Manage group order", "manage.order": "Manage order", "join.group.order": "Join group order", "join.group.text": "You can only select products from the restaurant chosen by the creator of the group", "join": "Join", "leave.group": "Leave group", "are.you.sure.leave.group": "Are you sure to leave group order?", "edit.order": "Edit order", "you.kicked.from.group": "You have been kicked from group order", "group.order.permission": "Some group members haven't finished making order. Are you sure to continue?", "see.all": "See all", "all.shops": "All shops", "shops": "Shops", "catalog": "Catalog", "ingredients": "Ingredients", "transaction.id": "Transaction ID", "wallet.history": "Wallet history", "sender": "Sender", "date": "Date", "note": "Note", "topup.wallet": "Topup wallet", "your.order": "Your order", "your.order.status.updated.text": "Your order status has been updated! Click 'Show' to see order details.", "help.center": "Help center", "message": "Message", "login.first": "Please, login first", "add.to.bag": "Add to bag", "be.seller": "Become seller", "general": "General", "logo.image": "Logo image", "background.image": "Background image", "delivery.info": "Delivery info", "minute": "Minute", "day": "Day", "month": "Month", "address": "Address", "seller.request.under.review": "Your request to become seller is currently under review.", "seller.request.accepted": "Your request to become seller is accepted.", "start.price": "Start price", "shop.closed": "Shop is closed", "no.zone.title": "We don't deliver here yet :(", "no.zone.text": "But we add dozens of new places every week. Maybe we'll be here soon! If you enter your email, we'll tell you as soon as we're available. We promise not to spam!", "payment.type": "Payment type", "verify": "Verify", "verify.email": "Email verification", "verify.text": "Please, enter the verification code we’ve sent you to", "verify.didntRecieveCode": "Didn’t receive the code?", "resend": "Send again", "should.match": "Passwords should match", "verify.send": "Verification code send successfully", "email.inuse": "The email has already been taken.", "verify.error": "Wrong verification code", "about": "About", "become.affiliate": "Become an Affiliate", "careers": "Careers", "blog": "Blog", "get.helps": "Get helps", "add.your.restaurant": "Add your restaurant", "sign.up.to.deliver": "Sign up to deliver", "privacy.policy": "Privacy Policy", "terms": "Terms", "tags": "Tags", "near_you": "Near you", "open_now": "Open now", "copy.code": "Copy code", "balance": "Balance", "referrals": "Referrals", "referral.title": "{{price_from}} for you, {{price_to}} for a friend", "referral.text": "Friends can get up to {{price_to}} off — you’ll get {{price_from}} when they place their first order.", "role": "Role", "category": "Category", "no.items": "No items", "referral.terms": "Referral terms", "login.or.create.account": "Login or create account", "sign.in.be.seller": "Sign in to be seller", "error.400": "Error occured. Please, try again later", "deals": "Deals", "more.info": "More info", "ratings": "Ratings", "open.until": "Open until", "no.orders.found": "You don't have any orders yet", "go.to.menu": "Go to menu", "no.refunds.found": "You don't have any order refunds yet. You can create a refund request from delivered orders.", "no.active.orders.found": "No active orders", "no.wallet.found": "You don't have any wallet transactions yet", "recent.searches": "Recent searches", "no.liked.restaurants": "You don't have any liked restaurants yet", "try.again": "Try again", "unauthorized": "Unauthorized", "you.cannot.join": "You cannot join. Invalid group order", "delivery.zone.not.available": "Sorry, we’re not available here", "leave.group.prompt": "You have joined in group order. In order to add product, leave group first!", "hours.ago": "hours ago", "become.delivery": "Become a delivery driver", "become.delivery.text": "Instead of traditional food delivery jobs where the hours aren’t flexible, try being your own boss with Foodyman. Get paid to deliver on your schedule using the food delivery app most downloaded by customers.", "discount": "Discount", "only.opened": "Only opened", "schedule": "Schedule", "shop.closed.choose.other.day": "Shop is closed in this day. Please, select another day.", "edit.schedule": "Edit schedule", "pickup.address": "Pickup address", "pickup.time": "Pickup time", "branch": "Branch", "branches": "Branches", "branches.not.found": "Branches not found", "out.of.stock": "Out of stock", "hour": "Hour", "h": "hour", "no.restaurants": "Restaurants not found according to your request", "no.shops": "Shops not found according to your request", "sms.not.sent": "Sms not sent!", "email.or.phone": "Email or phone", "login.invalid": "Login or password is invalid", "verify.phone": "Phone verification", "recipes": "Recipes", "recipes.title": "Recipes", "recipes.description": "Choose your favorite food recipe and buy as you wish", "no.recipes": "Recipes not found according to your request", "total.time": "Total time", "calories": "Calories", "servings": "Servings", "instructions": "Instructions", "nutritions": "Nutritions", "add.items.to.cart": "Add {{number}} items to cart", "recipe.discount.condition": "If you buy all ingredients you can get discount by", "go.to.recipe.order": "Ingredients added to cart successfully.", "recipe.discount.definition": "You got recipe discount", "insufficient.wallet.balance": "Insufficient wallet balance", "go.to.admin.panel": "Go to admin panel", "have.not.password": "You have not set password yet. Please, make sure you have a password in system before you create a request for become seller", "email": "Email", "edit.phone": "Edit phone", "verified": "Verified", "something.went.wrong": "Something went wrong", "phone.required": "Phone number is required", "no.careers.found": "Careers not found according to your request", "welcome.title": "Get your favorite foods delivered", "welcome.description": "Choose your address and start ordering", "do.you.have.restaurant": "Do you have a restaurant?", "deliver.title": "Looking for delivery driver jobs?", "welcome.features.title": "Other options for you", "start.ordering": "Start ordering", "why.choose.us": "Why choose us", "why.choose.us.first.title": "Choose what you want", "why.choose.us.first.text": "Select items from your favorite stores at Foodyman", "why.choose.us.second.title": "See real-time updates", "why.choose.us.second.text": "Personal shoppers pick items with care", "why.choose.us.third.title": "Get your items same-day", "why.choose.us.third.text": "<PERSON>joy Foodyman's 100% quality guarantee on every order", "choose.recomended.address": "Choose recomended address", "place.for.ad": "Place for your advertisement here", "ok": "Ok", "people.trust.us": "People trust us", "delivery.was.successfull": "Delivery was successfull", "view.our.insta": "View our Instagram", "latest.blog": "Latest blog", "ads": "Ads", "faq": "Frequently asked questions", "view.more": "View more", "transactions": "Transactions", "mark.read": "Mark all as read", "notifications": "Notifications", "no.notifications": "Notifications not found according to your request", "news": "News", "order.for.someone": "I want to order for someone", "user.details.empty": "Please, fill user details", "phone.invalid": "Phone number is invalid", "door.to.door.delivery": "Door to door delivery", "sender.details": "Sender details", "parcel.details": "Parcel details", "receiver.details": "Receiver details", "home": "Home", "stage": "Stage", "room": "Room", "active.parcels": "Active parcels", "parcel.history": "Parcel history", "receiver": "Receiver", "parcel": "<PERSON><PERSON><PERSON>", "parcel.cancelled": "Pa<PERSON>el cancelled", "phone.number": "Phone number", "type": "Type", "parcels": "<PERSON><PERSON><PERSON>", "sign.in.parcel.order": "Sign in to use door to door delivery", "up.to.weight": "up to {{ number }} kg", "up.to.length": "up to {{ number }} m", "length": "Length", "width": "<PERSON><PERSON><PERSON>", "height": "Height", "weight": "Weight", "hero.title": "Explore Our Shops with fast delivery", "offers": "Offers", "view.all": "View all", "number.of.offers": "{{number}} offers", "door.to.door.delivery.service": "Your personal door-to-door delivery service", "favorite.brands": "Favorite brands", "popular.near.you": "Popular near you", "daily.offers": "Daily offers", "follow.us": "Follow us on Social Media", "home.page": "Home page", "all.stories": "All stories", "categories": "Categories", "trending": "Trending", "delivery.free": "Delivery free", "delivery.with.in": "Delivery with in", "shop.banner.title": "Something hot. Something tasty.", "shop.banner.desc": "Top ratings and consistently great service", "order.now": "Order now", "error.something.went.wrong": "Oops, something went wrong!", "supported.image.formats.only": "Supported only image formats!", "invalid.image.source": "Invalid image source", "user.successfully.login": "User successfully logged in", "verify.code.sent": "Verification code sent", "empty": "Empty", "welcome": "Welcome", "image": "Image", "banner": "Banner", "brand.logo": "Brand logo", "brand.logo.dark": "Brand logo dark", "shop": "Shop"}