# Asaas Payment Integration - Comprehensive Analysis & Implementation Plan

## Executive Summary

This document provides a detailed technical analysis and implementation plan for integrating Asaas payment gateway with transparent checkout and split payment functionality into the TicketFlow platform. The integration will support credit cards, debit cards, and dynamic PIX payments with automatic commission splitting between restaurants, delivery drivers, and the platform.

## 1. Asaas Split Payment Analysis

### 1.1 How Asaas Split Payments Work

Based on official Asaas documentation analysis:

#### Split Payment Mechanism
- **Split Array**: Payments include a `split` array with `walletId` and value distribution
- **Automatic Distribution**: When payment is received, Asaas automatically transfers amounts to specified wallets
- **Value Types**: Supports both fixed values (`fixedValue`) and percentages (`percentualValue`)
- **Remaining Balance**: Unspecified amounts remain in the issuing account

#### Split Configuration Example
```json
{
  "split": [
    {
      "walletId": "********-9baa-4ec1-a11f-9010193527c6",
      "fixedValue": 20.00
    },
    {
      "walletId": "0b763922-aa88-4cbe-a567-e3fe8511fa06", 
      "percentualValue": 10.00
    }
  ]
}
```

#### Key Requirements for Split Payments
1. **Wallet IDs**: Each participant needs an Asaas account with `walletId`
2. **Account Creation**: Automatic subconta creation for restaurants and delivery drivers
3. **CPF/CNPJ Integration**: Direct transfers to CPF/CNPJ via Asaas accounts
4. **Percentage Calculation**: Based on net amount (after Asaas fees)
5. **Refund Handling**: Automatic split reversal on payment refunds

### 1.2 API Endpoints and Integration Requirements

#### Core Asaas API Endpoints
```
POST /v3/payments - Create payment with split
PUT /v3/payments/{id} - Update payment split
GET /v3/payments/{id} - Retrieve payment with split data
POST /v3/customers - Create customer/subconta
GET /v3/customers/{id} - Retrieve customer wallet info
```

#### Authentication
- **API Key**: Required in Authorization header
- **Environment**: Sandbox vs Production endpoints
- **Rate Limiting**: Standard API rate limits apply

## 2. Payment Method Requirements

### 2.1 Transparent Checkout Implementation

#### Credit Card Transparent Checkout
```json
{
  "customer": "cus_000005492075",
  "billingType": "CREDIT_CARD",
  "value": 100.00,
  "dueDate": "2024-12-31",
  "creditCard": {
    "holderName": "João Silva",
    "number": "****************",
    "expiryMonth": "05",
    "expiryYear": "2024",
    "ccv": "318"
  },
  "creditCardHolderInfo": {
    "name": "João Silva",
    "email": "<EMAIL>",
    "cpfCnpj": "12345678901",
    "postalCode": "89223-005",
    "addressNumber": "277",
    "phone": "4738010919"
  },
  "split": [
    {
      "walletId": "restaurant-wallet-id",
      "percentualValue": 70.00
    },
    {
      "walletId": "delivery-wallet-id", 
      "percentualValue": 15.00
    }
  ]
}
```

#### PIX Transparent Checkout
```json
{
  "customer": "cus_000005492075",
  "billingType": "PIX",
  "value": 100.00,
  "dueDate": "2024-12-31",
  "split": [
    {
      "walletId": "restaurant-wallet-id",
      "percentualValue": 70.00
    },
    {
      "walletId": "delivery-wallet-id",
      "percentualValue": 15.00
    }
  ]
}
```

#### Response Structure
```json
{
  "object": "payment",
  "id": "pay_123456789",
  "status": "PENDING",
  "pixTransaction": {
    "qrCode": {
      "encodedImage": "base64-encoded-qr-code",
      "payload": "pix-payload-string"
    },
    "expirationDate": "2024-12-31 23:59:59"
  },
  "split": [
    {
      "walletId": "restaurant-wallet-id",
      "percentualValue": 70.00,
      "value": 70.00
    }
  ]
}
```

### 2.2 Credit Card Tokenization
- **First Transaction**: Returns `creditCardToken` for future use
- **Subsequent Payments**: Use token instead of card details
- **Security**: Reduces PCI compliance requirements
- **Storage**: Tokens stored securely in database

## 3. Database Structure Analysis

### 3.1 Current Database Assessment

#### Existing Tables Analysis
```sql
-- Current relevant tables
users (id, firstname, lastname, email, phone, active)
shops (id, user_id, tax, percentage, phone, status)
orders (id, user_id, shop_id, total_price, status, deliveryman)
transactions (id, payable_type, payable_id, price, status, payment_sys_id)
payment_process (id, user_id, model_type, model_id, data)
payments (id, tag, active, sandbox)
payment_payloads (payment_id, payload)
```

#### Missing Fields for Brazilian Market
- **CPF/CNPJ fields** in users and shops tables
- **Asaas customer mapping** for wallet IDs
- **Split configuration storage**
- **Payment method preferences**

### 3.2 Required Database Modifications

#### New Tables for Asaas Integration
```sql
-- Asaas customer mapping
CREATE TABLE asaas_customers (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id),
    shop_id BIGINT REFERENCES shops(id) NULL,
    asaas_customer_id VARCHAR(255) UNIQUE NOT NULL,
    wallet_id VARCHAR(255) UNIQUE NOT NULL,
    customer_type ENUM('customer', 'restaurant', 'delivery_driver') NOT NULL,
    cpf_cnpj VARCHAR(14) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_shop_id (shop_id),
    INDEX idx_wallet_id (wallet_id)
);

-- Split payment configurations
CREATE TABLE payment_splits (
    id BIGSERIAL PRIMARY KEY,
    order_id BIGINT REFERENCES orders(id),
    asaas_payment_id VARCHAR(255) NOT NULL,
    restaurant_wallet_id VARCHAR(255) NOT NULL,
    delivery_wallet_id VARCHAR(255) NULL,
    platform_wallet_id VARCHAR(255) NOT NULL,
    restaurant_amount DECIMAL(10,2) NOT NULL,
    delivery_amount DECIMAL(10,2) DEFAULT 0,
    platform_amount DECIMAL(10,2) NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    split_status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_order_id (order_id),
    INDEX idx_asaas_payment_id (asaas_payment_id)
);

-- Credit card tokens storage
CREATE TABLE asaas_credit_card_tokens (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id),
    asaas_customer_id VARCHAR(255) NOT NULL,
    credit_card_token VARCHAR(255) NOT NULL,
    card_brand VARCHAR(50),
    card_last_four VARCHAR(4),
    holder_name VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_customer_id (asaas_customer_id)
);

-- Asaas payment transactions
CREATE TABLE asaas_payment_transactions (
    id BIGSERIAL PRIMARY KEY,
    order_id BIGINT REFERENCES orders(id),
    asaas_payment_id VARCHAR(255) UNIQUE NOT NULL,
    payment_method ENUM('CREDIT_CARD', 'DEBIT_CARD', 'PIX') NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    net_amount DECIMAL(10,2) NOT NULL,
    asaas_fee DECIMAL(10,2) NOT NULL,
    payment_status ENUM('PENDING', 'CONFIRMED', 'RECEIVED', 'OVERDUE', 'REFUNDED') NOT NULL,
    pix_qr_code TEXT NULL,
    pix_payload TEXT NULL,
    pix_expiration TIMESTAMP NULL,
    webhook_data JSON NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_order_id (order_id),
    INDEX idx_payment_id (asaas_payment_id),
    INDEX idx_status (payment_status)
);
```

#### Modifications to Existing Tables
```sql
-- Add CPF/CNPJ to users table
ALTER TABLE users ADD COLUMN cpf_cnpj VARCHAR(14) NULL;
ALTER TABLE users ADD COLUMN document_type ENUM('cpf', 'cnpj') NULL;
ALTER TABLE users ADD COLUMN is_delivery_driver BOOLEAN DEFAULT false;

-- Add CPF/CNPJ to shops table  
ALTER TABLE shops ADD COLUMN cnpj VARCHAR(14) NULL;
ALTER TABLE shops ADD COLUMN company_name VARCHAR(255) NULL;

-- Add Asaas payment method
INSERT INTO payments (tag, active, sandbox) VALUES ('asaas', 1, 1);
```

### 3.3 Data Storage Strategy

#### CPF/CNPJ Storage
- **Encryption**: Store encrypted CPF/CNPJ for security
- **Validation**: Brazilian CPF/CNPJ validation on input
- **Indexing**: Encrypted searchable indexes
- **Compliance**: LGPD compliance for personal data

#### Split Configuration Storage
- **Order-based**: Each order has specific split configuration
- **Flexible Percentages**: Support different commission rates per restaurant
- **Audit Trail**: Complete transaction history for reconciliation
- **Real-time Updates**: Split status tracking and updates

## 4. Flutter Mobile App Analysis

### 4.1 Current Payment Implementation Assessment

#### Current Flutter Payment Flow
```dart
// Current payment selection (redirect-based)
class PaymentMethods extends ConsumerStatefulWidget {
  // Simple payment method selection
  // Redirects to external payment gateway
  // No transparent checkout capability
}

// Current payment processing
Future<void> makePayment(BuildContext context, String name, int? orderId) async {
  final response = await _parcelRepository.process(orderId ?? 0, name);
  // Launches external URL - not transparent
  await launch(data, enableJavaScript: true);
}
```

#### Current Limitations
- **External Redirects**: All payments redirect to external gateways
- **No Card Forms**: No credit card input forms
- **No PIX Display**: No QR code display capability
- **Limited UX**: Poor user experience with redirects

### 4.2 Required Flutter Enhancements

#### Transparent Checkout Components
```dart
// Credit Card Form Widget
class AsaasCreditCardForm extends StatefulWidget {
  final Function(CreditCardData) onCardSubmit;
  final bool isLoading;
  
  @override
  _AsaasCreditCardFormState createState() => _AsaasCreditCardFormState();
}

class _AsaasCreditCardFormState extends State<AsaasCreditCardForm> {
  final _formKey = GlobalKey<FormState>();
  final _cardNumberController = TextEditingController();
  final _expiryController = TextEditingController();
  final _cvvController = TextEditingController();
  final _holderNameController = TextEditingController();
  
  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          // Card number input with formatting
          TextFormField(
            controller: _cardNumberController,
            decoration: InputDecoration(
              labelText: 'Card Number',
              hintText: '1234 5678 9012 3456',
            ),
            keyboardType: TextInputType.number,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              CardNumberInputFormatter(),
            ],
            validator: (value) => _validateCardNumber(value),
          ),
          
          Row(
            children: [
              // Expiry date input
              Expanded(
                child: TextFormField(
                  controller: _expiryController,
                  decoration: InputDecoration(
                    labelText: 'MM/YY',
                    hintText: '12/25',
                  ),
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    ExpiryDateInputFormatter(),
                  ],
                  validator: (value) => _validateExpiryDate(value),
                ),
              ),
              
              SizedBox(width: 16),
              
              // CVV input
              Expanded(
                child: TextFormField(
                  controller: _cvvController,
                  decoration: InputDecoration(
                    labelText: 'CVV',
                    hintText: '123',
                  ),
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(4),
                  ],
                  validator: (value) => _validateCVV(value),
                ),
              ),
            ],
          ),
          
          // Cardholder name
          TextFormField(
            controller: _holderNameController,
            decoration: InputDecoration(
              labelText: 'Cardholder Name',
              hintText: 'João Silva',
            ),
            textCapitalization: TextCapitalization.words,
            validator: (value) => _validateHolderName(value),
          ),
          
          SizedBox(height: 24),
          
          // Submit button
          ElevatedButton(
            onPressed: widget.isLoading ? null : _submitCard,
            child: widget.isLoading 
              ? CircularProgressIndicator()
              : Text('Pay Now'),
          ),
        ],
      ),
    );
  }
  
  void _submitCard() {
    if (_formKey.currentState!.validate()) {
      final cardData = CreditCardData(
        number: _cardNumberController.text.replaceAll(' ', ''),
        expiryMonth: _expiryController.text.split('/')[0],
        expiryYear: '20${_expiryController.text.split('/')[1]}',
        cvv: _cvvController.text,
        holderName: _holderNameController.text,
      );
      
      widget.onCardSubmit(cardData);
    }
  }
}
```

#### PIX Payment Widget
```dart
// PIX Payment Display Widget
class AsaasPixPayment extends StatefulWidget {
  final String qrCodeData;
  final String pixPayload;
  final DateTime expirationDate;
  final Function() onPaymentConfirmed;
  
  @override
  _AsaasPixPaymentState createState() => _AsaasPixPaymentState();
}

class _AsaasPixPaymentState extends State<AsaasPixPayment> {
  Timer? _paymentCheckTimer;
  bool _isCheckingPayment = false;
  
  @override
  void initState() {
    super.initState();
    _startPaymentPolling();
  }
  
  void _startPaymentPolling() {
    _paymentCheckTimer = Timer.periodic(Duration(seconds: 5), (timer) {
      _checkPaymentStatus();
    });
  }
  
  Future<void> _checkPaymentStatus() async {
    if (_isCheckingPayment) return;
    
    setState(() {
      _isCheckingPayment = true;
    });
    
    try {
      final status = await PaymentRepository().checkAsaasPaymentStatus(
        widget.paymentId
      );
      
      if (status == 'RECEIVED' || status == 'CONFIRMED') {
        _paymentCheckTimer?.cancel();
        widget.onPaymentConfirmed();
      }
    } catch (e) {
      // Handle error
    } finally {
      setState(() {
        _isCheckingPayment = false;
      });
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // QR Code Display
        Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: QrImage(
            data: widget.qrCodeData,
            version: QrVersions.auto,
            size: 200.0,
          ),
        ),
        
        SizedBox(height: 16),
        
        // Copy PIX payload button
        ElevatedButton.icon(
          onPressed: () {
            Clipboard.setData(ClipboardData(text: widget.pixPayload));
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('PIX code copied to clipboard')),
            );
          },
          icon: Icon(Icons.copy),
          label: Text('Copy PIX Code'),
        ),
        
        SizedBox(height: 16),
        
        // Expiration countdown
        StreamBuilder<Duration>(
          stream: _getCountdownStream(),
          builder: (context, snapshot) {
            if (snapshot.hasData) {
              final duration = snapshot.data!;
              return Text(
                'Expires in: ${duration.inMinutes}:${(duration.inSeconds % 60).toString().padLeft(2, '0')}',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: duration.inMinutes < 5 ? Colors.red : Colors.orange,
                ),
              );
            }
            return SizedBox.shrink();
          },
        ),
        
        SizedBox(height: 16),
        
        // Payment status indicator
        if (_isCheckingPayment)
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
              SizedBox(width: 8),
              Text('Checking payment status...'),
            ],
          ),
      ],
    );
  }
  
  Stream<Duration> _getCountdownStream() {
    return Stream.periodic(Duration(seconds: 1), (i) {
      final now = DateTime.now();
      final difference = widget.expirationDate.difference(now);
      return difference.isNegative ? Duration.zero : difference;
    });
  }
  
  @override
  void dispose() {
    _paymentCheckTimer?.cancel();
    super.dispose();
  }
}
```

#### Payment Method Selection Widget
```dart
// Enhanced Payment Method Selection
class AsaasPaymentMethods extends StatefulWidget {
  final OrderData order;
  final Function(PaymentResult) onPaymentComplete;

  @override
  _AsaasPaymentMethodsState createState() => _AsaasPaymentMethodsState();
}

class _AsaasPaymentMethodsState extends State<AsaasPaymentMethods> {
  PaymentMethod _selectedMethod = PaymentMethod.PIX;
  bool _isProcessing = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Payment method selection
        _buildPaymentMethodSelector(),

        SizedBox(height: 24),

        // Payment form based on selected method
        _buildPaymentForm(),
      ],
    );
  }

  Widget _buildPaymentMethodSelector() {
    return Row(
      children: [
        _buildMethodButton(
          method: PaymentMethod.PIX,
          icon: Icons.qr_code,
          label: 'PIX',
        ),
        SizedBox(width: 12),
        _buildMethodButton(
          method: PaymentMethod.CREDIT_CARD,
          icon: Icons.credit_card,
          label: 'Credit Card',
        ),
        SizedBox(width: 12),
        _buildMethodButton(
          method: PaymentMethod.DEBIT_CARD,
          icon: Icons.payment,
          label: 'Debit Card',
        ),
      ],
    );
  }

  Widget _buildMethodButton({
    required PaymentMethod method,
    required IconData icon,
    required String label,
  }) {
    final isSelected = _selectedMethod == method;

    return Expanded(
      child: GestureDetector(
        onTap: () => setState(() => _selectedMethod = method),
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 12, horizontal: 8),
          decoration: BoxDecoration(
            color: isSelected ? Colors.blue : Colors.grey[200],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isSelected ? Colors.blue : Colors.grey[300]!,
            ),
          ),
          child: Column(
            children: [
              Icon(
                icon,
                color: isSelected ? Colors.white : Colors.grey[600],
              ),
              SizedBox(height: 4),
              Text(
                label,
                style: TextStyle(
                  color: isSelected ? Colors.white : Colors.grey[600],
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPaymentForm() {
    switch (_selectedMethod) {
      case PaymentMethod.PIX:
        return _buildPixForm();
      case PaymentMethod.CREDIT_CARD:
      case PaymentMethod.DEBIT_CARD:
        return _buildCardForm();
    }
  }

  Widget _buildPixForm() {
    return Column(
      children: [
        Text(
          'PIX Payment',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        SizedBox(height: 16),
        Text(
          'Total: ${widget.order.totalPrice.toStringAsFixed(2)} BRL',
          style: TextStyle(fontSize: 16),
        ),
        SizedBox(height: 24),
        ElevatedButton(
          onPressed: _isProcessing ? null : _generatePixPayment,
          child: _isProcessing
            ? CircularProgressIndicator()
            : Text('Generate PIX Payment'),
        ),
      ],
    );
  }

  Widget _buildCardForm() {
    return AsaasCreditCardForm(
      onCardSubmit: _processCardPayment,
      isLoading: _isProcessing,
    );
  }

  Future<void> _generatePixPayment() async {
    setState(() => _isProcessing = true);

    try {
      final result = await PaymentRepository().createAsaasPixPayment(
        orderId: widget.order.id,
        amount: widget.order.totalPrice,
      );

      // Show PIX payment widget
      _showPixPaymentDialog(result);
    } catch (e) {
      _showErrorDialog(e.toString());
    } finally {
      setState(() => _isProcessing = false);
    }
  }

  Future<void> _processCardPayment(CreditCardData cardData) async {
    setState(() => _isProcessing = true);

    try {
      final result = await PaymentRepository().createAsaasCardPayment(
        orderId: widget.order.id,
        amount: widget.order.totalPrice,
        cardData: cardData,
        paymentMethod: _selectedMethod,
      );

      widget.onPaymentComplete(PaymentResult.success(result));
    } catch (e) {
      _showErrorDialog(e.toString());
    } finally {
      setState(() => _isProcessing = false);
    }
  }
}
```

## 5. Technical Implementation Plan

### 5.1 Backend Laravel Implementation

#### Asaas Service Class
```php
<?php

namespace App\Services\PaymentService;

use App\Models\Order;
use App\Models\User;
use App\Models\Shop;
use App\Models\AsaasCustomer;
use App\Models\AsaasPaymentTransaction;
use App\Models\PaymentSplit;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class AsaasService extends BaseService
{
    private string $apiKey;
    private string $baseUrl;

    public function __construct()
    {
        $this->apiKey = config('services.asaas.api_key');
        $this->baseUrl = config('services.asaas.base_url');
    }

    /**
     * Create or get Asaas customer for user
     */
    public function createOrGetCustomer(User $user, string $customerType = 'customer'): AsaasCustomer
    {
        // Check if customer already exists
        $asaasCustomer = AsaasCustomer::where('user_id', $user->id)
            ->where('customer_type', $customerType)
            ->first();

        if ($asaasCustomer) {
            return $asaasCustomer;
        }

        // Create new Asaas customer
        $response = Http::withHeaders([
            'access_token' => $this->apiKey,
            'Content-Type' => 'application/json',
        ])->post($this->baseUrl . '/customers', [
            'name' => $user->firstname . ' ' . $user->lastname,
            'email' => $user->email,
            'phone' => $user->phone,
            'cpfCnpj' => $user->cpf_cnpj,
            'notificationDisabled' => true,
        ]);

        if (!$response->successful()) {
            throw new \Exception('Failed to create Asaas customer: ' . $response->body());
        }

        $customerData = $response->json();

        // Store customer mapping
        return AsaasCustomer::create([
            'user_id' => $user->id,
            'asaas_customer_id' => $customerData['id'],
            'wallet_id' => $customerData['walletId'],
            'customer_type' => $customerType,
            'cpf_cnpj' => $user->cpf_cnpj,
        ]);
    }

    /**
     * Create PIX payment with split
     */
    public function createPixPayment(Order $order): array
    {
        $customer = $this->createOrGetCustomer($order->user);
        $splitConfig = $this->calculateSplitConfiguration($order);

        $response = Http::withHeaders([
            'access_token' => $this->apiKey,
            'Content-Type' => 'application/json',
        ])->post($this->baseUrl . '/payments', [
            'customer' => $customer->asaas_customer_id,
            'billingType' => 'PIX',
            'value' => $order->total_price,
            'dueDate' => now()->addHours(2)->format('Y-m-d'),
            'description' => "Order #{$order->id}",
            'split' => $splitConfig['splits'],
        ]);

        if (!$response->successful()) {
            throw new \Exception('Failed to create PIX payment: ' . $response->body());
        }

        $paymentData = $response->json();

        // Store payment transaction
        $this->storePaymentTransaction($order, $paymentData, 'PIX', $splitConfig);

        return [
            'payment_id' => $paymentData['id'],
            'qr_code' => $paymentData['pixTransaction']['qrCode']['encodedImage'],
            'pix_payload' => $paymentData['pixTransaction']['qrCode']['payload'],
            'expiration_date' => $paymentData['pixTransaction']['expirationDate'],
        ];
    }

    /**
     * Create credit card payment with split
     */
    public function createCreditCardPayment(Order $order, array $cardData): array
    {
        $customer = $this->createOrGetCustomer($order->user);
        $splitConfig = $this->calculateSplitConfiguration($order);

        $response = Http::withHeaders([
            'access_token' => $this->apiKey,
            'Content-Type' => 'application/json',
        ])->post($this->baseUrl . '/payments', [
            'customer' => $customer->asaas_customer_id,
            'billingType' => 'CREDIT_CARD',
            'value' => $order->total_price,
            'dueDate' => now()->format('Y-m-d'),
            'description' => "Order #{$order->id}",
            'creditCard' => [
                'holderName' => $cardData['holder_name'],
                'number' => $cardData['number'],
                'expiryMonth' => $cardData['expiry_month'],
                'expiryYear' => $cardData['expiry_year'],
                'ccv' => $cardData['cvv'],
            ],
            'creditCardHolderInfo' => [
                'name' => $cardData['holder_name'],
                'email' => $order->user->email,
                'cpfCnpj' => $order->user->cpf_cnpj,
                'postalCode' => $cardData['postal_code'],
                'addressNumber' => $cardData['address_number'],
                'phone' => $order->user->phone,
            ],
            'split' => $splitConfig['splits'],
        ]);

        if (!$response->successful()) {
            throw new \Exception('Failed to create credit card payment: ' . $response->body());
        }

        $paymentData = $response->json();

        // Store payment transaction
        $this->storePaymentTransaction($order, $paymentData, 'CREDIT_CARD', $splitConfig);

        // Store credit card token if provided
        if (isset($paymentData['creditCardToken'])) {
            $this->storeCreditCardToken($customer, $paymentData);
        }

        return [
            'payment_id' => $paymentData['id'],
            'status' => $paymentData['status'],
            'credit_card_token' => $paymentData['creditCardToken'] ?? null,
        ];
    }

    /**
     * Calculate split configuration for order
     */
    private function calculateSplitConfiguration(Order $order): array
    {
        $restaurant = $order->shop;
        $deliveryDriver = $order->deliveryman ? User::find($order->deliveryman) : null;

        // Get commission rates from settings
        $platformCommissionRate = (float) config('asaas.platform_commission_rate', 15); // 15%
        $deliveryCommissionRate = (float) config('asaas.delivery_commission_rate', 10); // 10%

        $totalAmount = $order->total_price;
        $platformAmount = $totalAmount * ($platformCommissionRate / 100);
        $deliveryAmount = $deliveryDriver ? ($order->delivery_fee * ($deliveryCommissionRate / 100)) : 0;
        $restaurantAmount = $totalAmount - $platformAmount - $deliveryAmount;

        $splits = [];

        // Restaurant split
        $restaurantCustomer = $this->createOrGetCustomer($restaurant->seller, 'restaurant');
        $splits[] = [
            'walletId' => $restaurantCustomer->wallet_id,
            'fixedValue' => round($restaurantAmount, 2),
        ];

        // Delivery driver split (if exists)
        if ($deliveryDriver && $deliveryAmount > 0) {
            $deliveryCustomer = $this->createOrGetCustomer($deliveryDriver, 'delivery_driver');
            $splits[] = [
                'walletId' => $deliveryCustomer->wallet_id,
                'fixedValue' => round($deliveryAmount, 2),
            ];
        }

        // Platform keeps the remaining amount (no split needed)

        return [
            'splits' => $splits,
            'restaurant_amount' => $restaurantAmount,
            'delivery_amount' => $deliveryAmount,
            'platform_amount' => $platformAmount,
            'total_amount' => $totalAmount,
        ];
    }

    /**
     * Store payment transaction record
     */
    private function storePaymentTransaction(Order $order, array $paymentData, string $paymentMethod, array $splitConfig): void
    {
        AsaasPaymentTransaction::create([
            'order_id' => $order->id,
            'asaas_payment_id' => $paymentData['id'],
            'payment_method' => $paymentMethod,
            'total_amount' => $splitConfig['total_amount'],
            'net_amount' => $paymentData['netValue'] ?? $splitConfig['total_amount'],
            'asaas_fee' => ($paymentData['value'] ?? $splitConfig['total_amount']) - ($paymentData['netValue'] ?? $splitConfig['total_amount']),
            'payment_status' => $paymentData['status'],
            'pix_qr_code' => $paymentData['pixTransaction']['qrCode']['encodedImage'] ?? null,
            'pix_payload' => $paymentData['pixTransaction']['qrCode']['payload'] ?? null,
            'pix_expiration' => isset($paymentData['pixTransaction']['expirationDate'])
                ? \Carbon\Carbon::parse($paymentData['pixTransaction']['expirationDate'])
                : null,
        ]);

        // Store split configuration
        PaymentSplit::create([
            'order_id' => $order->id,
            'asaas_payment_id' => $paymentData['id'],
            'restaurant_wallet_id' => $splitConfig['splits'][0]['walletId'],
            'delivery_wallet_id' => $splitConfig['splits'][1]['walletId'] ?? null,
            'platform_wallet_id' => config('asaas.platform_wallet_id'),
            'restaurant_amount' => $splitConfig['restaurant_amount'],
            'delivery_amount' => $splitConfig['delivery_amount'],
            'platform_amount' => $splitConfig['platform_amount'],
            'total_amount' => $splitConfig['total_amount'],
        ]);
    }

    /**
     * Handle Asaas webhook
     */
    public function handleWebhook(array $webhookData): void
    {
        $paymentId = $webhookData['payment']['id'];
        $status = $webhookData['payment']['status'];

        // Find payment transaction
        $paymentTransaction = AsaasPaymentTransaction::where('asaas_payment_id', $paymentId)->first();

        if (!$paymentTransaction) {
            Log::warning('Asaas webhook received for unknown payment', ['payment_id' => $paymentId]);
            return;
        }

        // Update payment status
        $paymentTransaction->update([
            'payment_status' => $status,
            'webhook_data' => $webhookData,
        ]);

        // Update split status
        $paymentSplit = PaymentSplit::where('asaas_payment_id', $paymentId)->first();
        if ($paymentSplit) {
            $splitStatus = match($status) {
                'RECEIVED', 'CONFIRMED' => 'completed',
                'REFUNDED' => 'refunded',
                default => 'pending',
            };

            $paymentSplit->update(['split_status' => $splitStatus]);
        }

        // Update order status if payment confirmed
        if (in_array($status, ['RECEIVED', 'CONFIRMED'])) {
            $order = $paymentTransaction->order;
            $order->update(['status' => Order::STATUS_ACCEPTED]);

            // Create transaction record for compatibility
            $order->createTransaction([
                'price' => $paymentTransaction->total_amount,
                'user_id' => $order->user_id,
                'payment_sys_id' => Payment::where('tag', 'asaas')->first()?->id,
                'payment_trx_id' => $paymentId,
                'note' => "Asaas payment for order #{$order->id}",
                'perform_time' => now(),
                'status' => Transaction::STATUS_PAID,
            ]);
        }
    }
}
```

#### API Controllers
```php
<?php

namespace App\Http\Controllers\API\v1\Dashboard\Payment;

use App\Http\Controllers\Controller;
use App\Http\Requests\Payment\AsaasPaymentRequest;
use App\Services\PaymentService\AsaasService;
use App\Models\Order;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class AsaasController extends Controller
{
    public function __construct(private AsaasService $asaasService)
    {
        parent::__construct();
    }

    /**
     * Create PIX payment
     */
    public function createPixPayment(AsaasPaymentRequest $request): JsonResponse
    {
        try {
            $order = Order::findOrFail($request->order_id);

            $result = $this->asaasService->createPixPayment($order);

            return response()->json([
                'success' => true,
                'data' => $result,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Create credit card payment
     */
    public function createCreditCardPayment(AsaasPaymentRequest $request): JsonResponse
    {
        try {
            $order = Order::findOrFail($request->order_id);

            $result = $this->asaasService->createCreditCardPayment($order, $request->card_data);

            return response()->json([
                'success' => true,
                'data' => $result,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Check payment status
     */
    public function checkPaymentStatus(string $paymentId): JsonResponse
    {
        try {
            $status = $this->asaasService->getPaymentStatus($paymentId);

            return response()->json([
                'success' => true,
                'status' => $status,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Handle Asaas webhook
     */
    public function webhook(Request $request): JsonResponse
    {
        try {
            $this->asaasService->handleWebhook($request->all());

            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            \Log::error('Asaas webhook error: ' . $e->getMessage(), [
                'webhook_data' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }
}
```

#### Request Validation
```php
<?php

namespace App\Http\Requests\Payment;

use Illuminate\Foundation\Http\FormRequest;

class AsaasPaymentRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $rules = [
            'order_id' => 'required|exists:orders,id',
        ];

        // Credit card specific validation
        if ($this->payment_method === 'CREDIT_CARD' || $this->payment_method === 'DEBIT_CARD') {
            $rules = array_merge($rules, [
                'card_data.holder_name' => 'required|string|max:255',
                'card_data.number' => 'required|string|size:16',
                'card_data.expiry_month' => 'required|string|size:2',
                'card_data.expiry_year' => 'required|string|size:4',
                'card_data.cvv' => 'required|string|min:3|max:4',
                'card_data.postal_code' => 'required|string|max:10',
                'card_data.address_number' => 'required|string|max:10',
            ]);
        }

        return $rules;
    }

    public function messages(): array
    {
        return [
            'order_id.required' => 'Order ID is required',
            'order_id.exists' => 'Order not found',
            'card_data.holder_name.required' => 'Cardholder name is required',
            'card_data.number.required' => 'Card number is required',
            'card_data.number.size' => 'Card number must be 16 digits',
            'card_data.expiry_month.required' => 'Expiry month is required',
            'card_data.expiry_year.required' => 'Expiry year is required',
            'card_data.cvv.required' => 'CVV is required',
        ];
    }
}
```

### 5.2 Database Models

#### Asaas Customer Model
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AsaasCustomer extends Model
{
    protected $fillable = [
        'user_id',
        'shop_id',
        'asaas_customer_id',
        'wallet_id',
        'customer_type',
        'cpf_cnpj',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function shop(): BelongsTo
    {
        return $this->belongsTo(Shop::class);
    }
}
```

#### Payment Split Model
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PaymentSplit extends Model
{
    protected $fillable = [
        'order_id',
        'asaas_payment_id',
        'restaurant_wallet_id',
        'delivery_wallet_id',
        'platform_wallet_id',
        'restaurant_amount',
        'delivery_amount',
        'platform_amount',
        'total_amount',
        'split_status',
    ];

    protected $casts = [
        'restaurant_amount' => 'decimal:2',
        'delivery_amount' => 'decimal:2',
        'platform_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
    ];

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }
}
```

### 5.3 API Routes
```php
// routes/api.php additions
Route::group(['prefix' => 'payments/asaas'], function () {
    Route::post('pix/create', [AsaasController::class, 'createPixPayment']);
    Route::post('credit-card/create', [AsaasController::class, 'createCreditCardPayment']);
    Route::get('status/{paymentId}', [AsaasController::class, 'checkPaymentStatus']);
});

// Webhook route
Route::post('webhook/asaas/payment', [AsaasController::class, 'webhook']);
```

### 5.4 Configuration
```php
// config/services.php
'asaas' => [
    'api_key' => env('ASAAS_API_KEY'),
    'base_url' => env('ASAAS_BASE_URL', 'https://api-sandbox.asaas.com/v3'),
    'platform_wallet_id' => env('ASAAS_PLATFORM_WALLET_ID'),
    'platform_commission_rate' => env('ASAAS_PLATFORM_COMMISSION_RATE', 15),
    'delivery_commission_rate' => env('ASAAS_DELIVERY_COMMISSION_RATE', 10),
],
```

## 6. Error Handling & Fallback Mechanisms

### 6.1 Payment Failure Handling
```php
class AsaasErrorHandler
{
    public function handlePaymentFailure(\Exception $e, Order $order): array
    {
        // Log error
        \Log::error('Asaas payment failed', [
            'order_id' => $order->id,
            'error' => $e->getMessage(),
        ]);

        // Determine fallback strategy
        if ($this->isTemporaryError($e)) {
            return [
                'action' => 'retry',
                'message' => 'Temporary error, please try again',
                'retry_after' => 30, // seconds
            ];
        }

        if ($this->isCardError($e)) {
            return [
                'action' => 'card_error',
                'message' => 'Card declined, please try another payment method',
                'suggested_methods' => ['PIX'],
            ];
        }

        // Fallback to other payment methods
        return [
            'action' => 'fallback',
            'message' => 'Payment service unavailable, please try another method',
            'available_methods' => $this->getAvailablePaymentMethods($order->shop),
        ];
    }

    private function isTemporaryError(\Exception $e): bool
    {
        $temporaryErrors = [
            'timeout',
            'connection_error',
            'service_unavailable',
        ];

        foreach ($temporaryErrors as $error) {
            if (str_contains(strtolower($e->getMessage()), $error)) {
                return true;
            }
        }

        return false;
    }
}
```

### 6.2 Network Resilience
```dart
// Flutter network resilience
class AsaasPaymentRepository {
  static const int maxRetries = 3;
  static const Duration retryDelay = Duration(seconds: 2);

  Future<PaymentResult> createPaymentWithRetry({
    required int orderId,
    required double amount,
    required PaymentMethod method,
    Map<String, dynamic>? cardData,
  }) async {
    int attempts = 0;

    while (attempts < maxRetries) {
      try {
        return await _createPayment(
          orderId: orderId,
          amount: amount,
          method: method,
          cardData: cardData,
        );
      } catch (e) {
        attempts++;

        if (attempts >= maxRetries) {
          throw PaymentException('Payment failed after $maxRetries attempts: $e');
        }

        if (_isRetryableError(e)) {
          await Future.delayed(retryDelay * attempts);
          continue;
        }

        // Non-retryable error, throw immediately
        throw e;
      }
    }

    throw PaymentException('Unexpected error in payment retry logic');
  }

  bool _isRetryableError(dynamic error) {
    if (error is DioError) {
      return error.type == DioErrorType.connectTimeout ||
             error.type == DioErrorType.receiveTimeout ||
             error.type == DioErrorType.sendTimeout ||
             (error.response?.statusCode ?? 0) >= 500;
    }

    return false;
  }
}
```

## 7. Development Timeline & Resource Requirements

### 7.1 Implementation Phases

#### Phase 1: Backend Foundation (3-4 weeks)
- **Week 1-2**: Database migrations and models
- **Week 2-3**: Asaas service implementation
- **Week 3-4**: API controllers and validation

#### Phase 2: Flutter Integration (3-4 weeks)
- **Week 1-2**: Payment form widgets
- **Week 2-3**: PIX payment components
- **Week 3-4**: Payment flow integration

#### Phase 3: Testing & Optimization (2-3 weeks)
- **Week 1**: Unit and integration testing
- **Week 2**: End-to-end testing
- **Week 3**: Performance optimization

#### Phase 4: Production Deployment (1-2 weeks)
- **Week 1**: Production configuration
- **Week 2**: Monitoring and rollout

### 7.2 Resource Requirements

#### Development Team
- **1 Senior Backend Developer** (Laravel/PHP specialist)
- **1 Senior Mobile Developer** (Flutter specialist)
- **1 Payment Integration Specialist** (Asaas/Brazilian payments)
- **1 QA Engineer** (Payment testing specialist)

#### Infrastructure Requirements
- **Enhanced server capacity** for payment processing
- **SSL certificates** for PCI compliance
- **Monitoring tools** for payment tracking
- **Backup systems** for transaction data

### 7.3 Estimated Costs
- **Development**: $80,000 - $120,000 USD
- **Infrastructure**: $5,000 - $10,000 USD
- **Testing & QA**: $15,000 - $25,000 USD
- **Compliance & Legal**: $10,000 - $15,000 USD
- **Total**: $110,000 - $170,000 USD

## 8. Success Metrics & KPIs

### 8.1 Technical Metrics
- **Payment Success Rate**: Target 98%+
- **Average Processing Time**: <3 seconds
- **API Response Time**: <500ms
- **Error Rate**: <2%

### 8.2 Business Metrics
- **Split Payment Adoption**: 80%+ of orders
- **PIX Usage**: 60%+ of payments
- **Customer Satisfaction**: 4.5+ rating
- **Revenue Growth**: 25%+ increase

### 8.3 Monitoring Dashboard
```php
class AsaasMetricsService
{
    public function getPaymentMetrics(): array
    {
        return [
            'total_payments' => AsaasPaymentTransaction::count(),
            'success_rate' => $this->calculateSuccessRate(),
            'average_processing_time' => $this->getAverageProcessingTime(),
            'pix_adoption_rate' => $this->getPixAdoptionRate(),
            'split_payment_volume' => $this->getSplitPaymentVolume(),
            'commission_collected' => $this->getTotalCommissionCollected(),
        ];
    }
}
```

This comprehensive implementation plan provides a complete roadmap for integrating Asaas payment gateway with transparent checkout and split payment functionality into the TicketFlow platform, specifically designed for the Brazilian food delivery market.
```
